    # docker-compose.yml
    # Configuración de producción con Trae<PERSON>k y Cloudflare Tunnel integrado.

    services:
      # --- <PERSON><PERSON><PERSON> (Reverse Proxy Interno) ---
      traefik:
        image: 'traefik'
        restart: always
        command:
          - '--api.insecure=true'
          - '--api.dashboard=true'
          - '--providers.docker=true'
          - '--providers.docker.exposedbydefault=false'
          - '--entrypoints.websecure.address=:443'
          # Ya no necesitamos Let's Encrypt porque Cloudflare gestionará el SSL externo.
          # Traefik puede usar un certificado autofirmado para la comunicación interna.
        expose:
          - "443"
          - "80"
        volumes:
          - /var/run/docker.sock:/var/run/docker.sock:ro

      # --- Contenedor de Inicialización (Sin cambios) ---
      initContainer:
        image: busybox
        command: ['sh', '-c', 'chown -R 1000:1000 /home/<USER>/.n8n']
        volumes:
          - ${DATA_FOLDER}/.n8n:/home/<USER>/.n8n

      # --- <PERSON><PERSON><PERSON> n8n (Sin cambios, pero ya no es directamente accesible) ---
      n8n:
        image: docker.n8n.io/n8nio/n8n
        restart: always
        ports:
          - '127.0.0.1:5678:5678'
        labels:
          - traefik.enable=true
          - traefik.http.routers.n8n.rule=Host(`${SUBDOMAIN}.${DOMAIN_NAME}`)
          - traefik.http.routers.n8n.entrypoints=websecure
          # Habilitamos TLS para que Traefik genere un certificado autofirmado.
          - traefik.http.routers.n8n.tls=true
          # Le decimos a Traefik que el servicio está en el puerto 5678
          - traefik.http.services.n8n.loadbalancer.server.port=5678
        environment:
          - N8N_HOST=${SUBDOMAIN}.${DOMAIN_NAME}
          - N8N_PORT=5678
          - N8N_PROTOCOL=https
          - NODE_ENV=production
          - WEBHOOK_URL=https://${SUBDOMAIN}.${DOMAIN_NAME}
          - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
        volumes:
          - ${DATA_FOLDER}/.n8n:/home/<USER>/.n8n
        depends_on:
          initContainer:
            condition: service_completed_successfully

      # --- NUEVO SERVICIO: Conector del Túnel de Cloudflare ---
      cloudflared:
        image: cloudflare/cloudflared:latest
        restart: always
        command: tunnel --no-autoupdate run
        environment:
          - TUNNEL_TOKEN=${CLOUDFLARE_TUNNEL_TOKEN}
        depends_on:
          - traefik
    
